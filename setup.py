"""
文化符号分析Agent安装脚本
自动化环境设置和依赖安装
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n🔸 步骤{step}: {description}")

def check_python_version():
    """检查Python版本"""
    print_step(1, "检查Python版本")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def check_pip():
    """检查pip是否可用"""
    print_step(2, "检查pip")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip可用")
        return True
    except subprocess.CalledProcessError:
        print("❌ 错误: pip不可用")
        return False

def install_dependencies():
    """安装依赖"""
    print_step(3, "安装依赖包")
    
    try:
        print("📦 正在安装依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        print("✅ 依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 错误: requirements.txt文件不存在")
        return False

def setup_environment():
    """设置环境变量"""
    print_step(4, "设置环境变量")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("⚠️  .env文件已存在，跳过创建")
        return True
    
    if not env_example.exists():
        print("❌ 错误: .env.example文件不存在")
        return False
    
    try:
        shutil.copy(env_example, env_file)
        print("✅ 已创建.env文件")
        print("💡 请编辑.env文件并填入您的API密钥")
        return True
    except Exception as e:
        print(f"❌ 创建.env文件失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print_step(5, "创建目录结构")
    
    directories = ["logs", "data", "temp"]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✅ 创建目录: {directory}")
            except Exception as e:
                print(f"❌ 创建目录失败 {directory}: {e}")
                return False
        else:
            print(f"📁 目录已存在: {directory}")
    
    return True

def run_tests():
    """运行测试"""
    print_step(6, "运行测试")
    
    try:
        print("🧪 正在运行测试...")
        result = subprocess.run([sys.executable, "test_agent.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 所有测试通过")
            return True
        else:
            print("❌ 测试失败")
            print("错误输出:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False

def verify_installation():
    """验证安装"""
    print_step(7, "验证安装")
    
    try:
        # 尝试导入主要模块
        import cultural_symbol_agent
        import config
        import logger
        
        print("✅ 模块导入成功")
        
        # 检查配置
        from config import get_config
        config_obj = get_config()
        
        if config_obj.llm.api_key:
            print("✅ API密钥已配置")
        else:
            print("⚠️  API密钥未配置，请编辑.env文件")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print_header("安装完成")
    
    print("🎉 文化符号分析Agent安装成功！")
    print("\n📋 后续步骤:")
    print("1. 编辑.env文件，填入您的Kimi API密钥")
    print("2. 运行 'python main.py' 开始使用")
    print("3. 运行 'python example_usage.py' 查看使用示例")
    print("4. 运行 'python test_agent.py' 进行功能测试")
    
    print("\n📚 文档:")
    print("- README.md: 详细使用说明")
    print("- config.py: 配置选项说明")
    print("- logger.py: 日志系统说明")
    
    print("\n🔗 相关链接:")
    print("- Kimi API: https://platform.moonshot.cn/")
    print("- LangChain文档: https://python.langchain.com/")

def main():
    """主安装流程"""
    print_header("文化符号分析Agent安装程序")
    
    print("🚀 开始安装文化符号分析Agent...")
    
    # 检查系统要求
    if not check_python_version():
        return False
    
    if not check_pip():
        return False
    
    # 安装和配置
    steps = [
        install_dependencies,
        setup_environment,
        create_directories,
        verify_installation
    ]
    
    for step_func in steps:
        if not step_func():
            print("\n❌ 安装失败，请检查错误信息")
            return False
    
    # 可选测试
    print("\n🧪 是否运行测试？(y/n): ", end="")
    if input().lower().startswith('y'):
        run_tests()
    
    show_next_steps()
    return True

def uninstall():
    """卸载程序"""
    print_header("卸载文化符号分析Agent")
    
    print("⚠️  这将删除以下内容:")
    print("- 依赖包")
    print("- 日志文件")
    print("- 临时文件")
    print("- .env文件 (如果存在)")
    
    confirm = input("\n确认卸载？(y/N): ")
    if not confirm.lower().startswith('y'):
        print("取消卸载")
        return
    
    try:
        # 删除目录
        directories = ["logs", "data", "temp"]
        for directory in directories:
            dir_path = Path(directory)
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"✅ 删除目录: {directory}")
        
        # 删除.env文件
        env_file = Path(".env")
        if env_file.exists():
            env_file.unlink()
            print("✅ 删除.env文件")
        
        print("✅ 卸载完成")
        
    except Exception as e:
        print(f"❌ 卸载失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "uninstall":
        uninstall()
    else:
        main()
