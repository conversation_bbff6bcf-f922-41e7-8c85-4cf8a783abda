"""
日志系统
提供统一的日志记录功能
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional
from config import get_config

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class AgentLogger:
    """Agent日志记录器"""
    
    def __init__(self, name: str = "CulturalSymbolAgent"):
        self.config = get_config()
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 设置日志级别
        level = getattr(logging, self.config.system.log_level.upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        colored_formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(colored_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器（如果启用调试模式）
        if self.config.system.debug:
            self._add_file_handler(formatter)
    
    def _add_file_handler(self, formatter):
        """添加文件处理器"""
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 创建日志文件名
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = os.path.join(log_dir, f"agent_{timestamp}.log")
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, **kwargs)
    
    def log_workflow_state(self, old_state: str, new_state: str):
        """记录工作流程状态变化"""
        self.info(f"工作流程状态变化: {old_state} → {new_state}")
    
    def log_tool_call(self, tool_name: str, action: str, success: bool = True):
        """记录工具调用"""
        status = "成功" if success else "失败"
        self.info(f"工具调用 [{tool_name}] {action}: {status}")
    
    def log_user_interaction(self, user_input: str, agent_response: str):
        """记录用户交互"""
        self.debug(f"用户输入: {user_input[:100]}...")
        self.debug(f"Agent响应: {agent_response[:100]}...")
    
    def log_error_with_context(self, error: Exception, context: str = ""):
        """记录带上下文的错误"""
        error_msg = f"错误: {str(error)}"
        if context:
            error_msg += f" | 上下文: {context}"
        self.error(error_msg)
    
    def log_performance(self, operation: str, duration: float):
        """记录性能指标"""
        self.debug(f"性能指标 [{operation}]: {duration:.2f}秒")

# 全局日志记录器实例
_logger_instance: Optional[AgentLogger] = None

def get_logger() -> AgentLogger:
    """获取全局日志记录器实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = AgentLogger()
    return _logger_instance

def log_debug(message: str, **kwargs):
    """快捷调试日志函数"""
    get_logger().debug(message, **kwargs)

def log_info(message: str, **kwargs):
    """快捷信息日志函数"""
    get_logger().info(message, **kwargs)

def log_warning(message: str, **kwargs):
    """快捷警告日志函数"""
    get_logger().warning(message, **kwargs)

def log_error(message: str, **kwargs):
    """快捷错误日志函数"""
    get_logger().error(message, **kwargs)

def log_critical(message: str, **kwargs):
    """快捷严重错误日志函数"""
    get_logger().critical(message, **kwargs)

# 装饰器函数
def log_function_call(func):
    """函数调用日志装饰器"""
    def wrapper(*args, **kwargs):
        logger = get_logger()
        func_name = func.__name__
        logger.debug(f"调用函数: {func_name}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func_name} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func_name} 执行失败: {str(e)}")
            raise
    
    return wrapper

def log_tool_execution(tool_name: str):
    """工具执行日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger()
            logger.info(f"开始执行工具: {tool_name}")
            
            try:
                result = func(*args, **kwargs)
                logger.info(f"工具 {tool_name} 执行成功")
                return result
            except Exception as e:
                logger.error(f"工具 {tool_name} 执行失败: {str(e)}")
                raise
        
        return wrapper
    return decorator

if __name__ == "__main__":
    # 测试日志系统
    logger = get_logger()
    
    logger.debug("这是一条调试消息")
    logger.info("这是一条信息消息")
    logger.warning("这是一条警告消息")
    logger.error("这是一条错误消息")
    logger.critical("这是一条严重错误消息")
    
    # 测试特殊日志方法
    logger.log_workflow_state("状态A", "状态B")
    logger.log_tool_call("测试工具", "测试操作", True)
    logger.log_user_interaction("用户输入测试", "Agent响应测试")
    logger.log_performance("测试操作", 1.23)
