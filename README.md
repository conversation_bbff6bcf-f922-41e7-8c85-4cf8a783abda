# 文化符号分析Agent

基于LangChain和Kimi-K2开发的智能文化符号分析系统，能够帮助用户分析客户的文化符号特征。

## 功能特点

- 🎯 **智能工作流程**: 按照预定义的工作流程引导用户完成分析
- 🤖 **Kimi-K2驱动**: 使用先进的Kimi-K2大语言模型
- 💾 **对话记忆**: 保存最近3轮对话历史
- 🛠️ **模块化工具**: 包含客户信息管理、工作流程控制等多个工具
- 🔄 **错误处理**: 完善的错误处理和恢复机制

## 工作流程

```mermaid
graph TD
    A[输入客户名称] --> B{开始搜索}
    B -->|No| C[提问式让用户回答收集客户相关资料]
    B -->|Yes| D[按格式填写文化符号提示词，提示用户需上传附件]
    C --> D
    D --> E{反问用户3个问题收集信息}
    E -->|No| F[完成3个文化符号总结]
    E -->|Yes| F
    F --> G{结果选择}
    G -->|No| F
    G -->|Yes| H[完成]
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并填入您的配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# Kimi API配置
KIMI_API_KEY=your_kimi_api_key_here
KIMI_BASE_URL=https://api.moonshot.cn/v1

# 其他配置
DEBUG=True
```

### 3. 运行系统

```bash
python main.py
```

## 使用方法

### 交互模式

选择交互模式后，您可以与Agent进行自然语言对话：

```
👤 您: 你好，我想分析苹果公司的文化符号
🤖 助手: 您好！我是文化符号分析助手。我已经记录了您要分析的客户是"苹果公司"...
```

### 演示模式

演示模式会自动运行一个完整的分析流程，展示系统的各项功能。

### 工具测试模式

测试模式用于验证各个工具组件的功能是否正常。

## 系统架构

### 核心组件

1. **CulturalSymbolAgent**: 主Agent类，协调整个分析流程
2. **CustomerInfoTool**: 客户信息管理工具
3. **WorkflowTool**: 工作流程控制工具
4. **QuestionGeneratorTool**: 问题生成工具
5. **CulturalSymbolAnalyzer**: 文化符号分析工具

### 工作流程状态

- `INPUT_CUSTOMER_NAME`: 输入客户名称
- `SEARCH_DECISION`: 搜索决策
- `COLLECT_MATERIALS`: 收集资料
- `FORMAT_PROMPT`: 格式化提示
- `ASK_QUESTIONS`: 提问阶段
- `GENERATE_SUMMARY`: 生成总结
- `RESULT_SELECTION`: 结果选择
- `COMPLETED`: 完成

## API参考

### CulturalSymbolAgent

```python
from cultural_symbol_agent import CulturalSymbolAgent

# 初始化Agent
agent = CulturalSymbolAgent()

# 处理用户输入
response = agent.run("你好，我想分析客户的文化符号")

# 获取当前状态
state = agent.get_current_state()

# 获取客户信息
info = agent.get_customer_info()

# 清空对话记忆
agent.clear_memory()
```

### 工具使用示例

```python
# 客户信息工具
customer_tool = CustomerInfoTool()
customer_tool._run("set_name", "苹果公司")
customer_tool._run("add_material", "公司简介")

# 工作流程工具
workflow_tool = WorkflowTool()
workflow_tool._run("set_state", "collect_materials")

# 问题生成工具
question_tool = QuestionGeneratorTool()
questions = question_tool._run("苹果公司")

# 文化符号分析工具
analyzer = CulturalSymbolAnalyzer()
symbols = analyzer._run(customer_info_json)
```

## 错误处理

系统包含完善的错误处理机制：

- 工具调用失败时返回"服务暂时不可用"
- 自动重试机制
- 详细的错误日志记录
- 优雅的降级处理

## 扩展开发

### 添加新工具

1. 继承 `BaseTool` 类
2. 实现 `_run` 方法
3. 在 `CulturalSymbolAgent._init_tools()` 中注册

### 自定义工作流程

修改 `WorkflowState` 枚举和相关逻辑来自定义工作流程。

### 集成其他LLM

修改 `_init_llm()` 方法来使用其他大语言模型。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
