"""
文化符号分析Agent测试文件
用于测试各个组件的功能
"""

import unittest
import json
import os
from unittest.mock import patch, MagicMock

from cultural_symbol_agent import (
    CulturalSymbolAgent,
    CustomerInfoTool,
    WorkflowTool,
    QuestionGeneratorTool,
    CulturalSymbolAnalyzer,
    WorkflowState,
    CustomerInfo
)

class TestCustomerInfo(unittest.TestCase):
    """测试CustomerInfo数据类"""
    
    def test_customer_info_initialization(self):
        """测试CustomerInfo初始化"""
        info = CustomerInfo()
        self.assertEqual(info.name, "")
        self.assertEqual(info.materials, [])
        self.assertEqual(info.questions_answers, {})
        self.assertEqual(info.cultural_symbols, [])
    
    def test_customer_info_with_data(self):
        """测试带数据的CustomerInfo"""
        info = CustomerInfo(
            name="测试公司",
            materials=["资料1"],
            questions_answers={"问题1": "答案1"}
        )
        self.assertEqual(info.name, "测试公司")
        self.assertEqual(len(info.materials), 1)
        self.assertEqual(len(info.questions_answers), 1)

class TestCustomerInfoTool(unittest.TestCase):
    """测试CustomerInfoTool"""
    
    def setUp(self):
        self.tool = CustomerInfoTool()
    
    def test_set_name(self):
        """测试设置客户名称"""
        result = self.tool._run("set_name", "苹果公司")
        self.assertIn("苹果公司", result)
        self.assertEqual(self.tool.customer_info.name, "苹果公司")
    
    def test_add_material(self):
        """测试添加资料"""
        result = self.tool._run("add_material", "公司简介")
        self.assertIn("公司简介", result)
        self.assertIn("公司简介", self.tool.customer_info.materials)
    
    def test_add_qa(self):
        """测试添加问答"""
        qa_data = '{"问题1": "答案1"}'
        result = self.tool._run("add_qa", qa_data)
        self.assertIn("问答信息", result)
        self.assertEqual(self.tool.customer_info.questions_answers["问题1"], "答案1")
    
    def test_get_info(self):
        """测试获取信息"""
        self.tool._run("set_name", "测试公司")
        self.tool._run("add_material", "资料1")
        
        result = self.tool._run("get_info")
        info = json.loads(result)
        
        self.assertEqual(info["name"], "测试公司")
        self.assertIn("资料1", info["materials"])
    
    def test_invalid_action(self):
        """测试无效操作"""
        result = self.tool._run("invalid_action")
        self.assertIn("不支持的操作", result)

class TestWorkflowTool(unittest.TestCase):
    """测试WorkflowTool"""
    
    def setUp(self):
        self.tool = WorkflowTool()
    
    def test_get_initial_state(self):
        """测试获取初始状态"""
        result = self.tool._run("get_state")
        self.assertEqual(result, WorkflowState.INPUT_CUSTOMER_NAME.value)
    
    def test_set_state(self):
        """测试设置状态"""
        result = self.tool._run("set_state", WorkflowState.COLLECT_MATERIALS.value)
        self.assertIn("工作流程状态已更新", result)
        self.assertEqual(self.tool.current_state, WorkflowState.COLLECT_MATERIALS)
    
    def test_get_history(self):
        """测试获取历史"""
        # 改变几次状态
        self.tool._run("set_state", WorkflowState.COLLECT_MATERIALS.value)
        self.tool._run("set_state", WorkflowState.ASK_QUESTIONS.value)
        
        result = self.tool._run("get_history")
        history = json.loads(result)
        
        self.assertGreater(len(history), 0)
        self.assertIn(WorkflowState.INPUT_CUSTOMER_NAME.value, history)

class TestQuestionGeneratorTool(unittest.TestCase):
    """测试QuestionGeneratorTool"""
    
    def setUp(self):
        self.tool = QuestionGeneratorTool()
    
    def test_generate_questions(self):
        """测试生成问题"""
        result = self.tool._run("苹果公司")
        questions = json.loads(result)
        
        self.assertEqual(len(questions), 3)
        self.assertTrue(all("苹果公司" in q for q in questions))

class TestCulturalSymbolAnalyzer(unittest.TestCase):
    """测试CulturalSymbolAnalyzer"""
    
    def setUp(self):
        self.tool = CulturalSymbolAnalyzer()
    
    def test_analyze_symbols(self):
        """测试分析文化符号"""
        test_info = {
            "name": "苹果公司",
            "materials": ["公司简介", "产品介绍"],
            "questions_answers": {
                "问题1": "苹果是一家创新的科技公司",
                "问题2": "专注于高品质的消费电子产品"
            }
        }
        
        result = self.tool._run(json.dumps(test_info))
        symbols = json.loads(result)
        
        self.assertGreater(len(symbols), 0)
        self.assertTrue(any("苹果公司" in symbol for symbol in symbols))

class TestCulturalSymbolAgent(unittest.TestCase):
    """测试CulturalSymbolAgent主类"""
    
    @patch.dict(os.environ, {
        'KIMI_API_KEY': 'test_key',
        'KIMI_BASE_URL': 'https://test.api.com'
    })
    def setUp(self):
        """设置测试环境"""
        # 模拟LLM以避免实际API调用
        with patch('cultural_symbol_agent.ChatOpenAI') as mock_llm:
            mock_llm.return_value = MagicMock()
            self.agent = CulturalSymbolAgent()
    
    def test_agent_initialization(self):
        """测试Agent初始化"""
        self.assertIsNotNone(self.agent.llm)
        self.assertIsNotNone(self.agent.memory)
        self.assertIsNotNone(self.agent.tools)
        self.assertIsNotNone(self.agent.agent_executor)
        self.assertEqual(len(self.agent.tools), 4)
    
    def test_get_current_state(self):
        """测试获取当前状态"""
        state = self.agent.get_current_state()
        self.assertIsInstance(state, str)
    
    def test_get_customer_info(self):
        """测试获取客户信息"""
        info = self.agent.get_customer_info()
        self.assertIsInstance(info, dict)
    
    def test_clear_memory(self):
        """测试清空记忆"""
        # 添加一些消息到记忆中
        self.agent.memory.chat_memory.add_user_message("测试消息")
        self.assertGreater(len(self.agent.memory.chat_memory.messages), 0)
        
        # 清空记忆
        self.agent.clear_memory()
        self.assertEqual(len(self.agent.memory.chat_memory.messages), 0)

class TestWorkflowIntegration(unittest.TestCase):
    """测试工作流程集成"""
    
    def test_workflow_states(self):
        """测试工作流程状态枚举"""
        states = list(WorkflowState)
        expected_states = [
            WorkflowState.INPUT_CUSTOMER_NAME,
            WorkflowState.SEARCH_DECISION,
            WorkflowState.COLLECT_MATERIALS,
            WorkflowState.FORMAT_PROMPT,
            WorkflowState.ASK_QUESTIONS,
            WorkflowState.GENERATE_SUMMARY,
            WorkflowState.RESULT_SELECTION,
            WorkflowState.COMPLETED
        ]
        
        self.assertEqual(len(states), len(expected_states))
        for state in expected_states:
            self.assertIn(state, states)

def run_tests():
    """运行所有测试"""
    print("🧪 开始运行文化符号分析Agent测试...")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestCustomerInfo,
        TestCustomerInfoTool,
        TestWorkflowTool,
        TestQuestionGeneratorTool,
        TestCulturalSymbolAnalyzer,
        TestCulturalSymbolAgent,
        TestWorkflowIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ 所有测试通过！")
    else:
        print(f"❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_tests()
